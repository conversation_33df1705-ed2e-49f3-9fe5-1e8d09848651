// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pdf_file.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class PdfFileAdapter extends TypeAdapter<PdfFile> {
  @override
  final int typeId = 2;

  @override
  PdfFile read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return PdfFile(
      id: fields[0] as String,
      name: fields[1] as String,
      subject: fields[2] as String,
      category: fields[3] as String,
      description: fields[4] as String?,
      onlineUrl: fields[5] as String?,
      localPath: fields[6] as String?,
      fileSize: fields[7] as int,
      uploadDate: fields[8] as DateTime,
      downloadDate: fields[9] as DateTime,
      isDownloaded: fields[10] as bool,
      isAvailableOffline: fields[11] as bool,
      thumbnailPath: fields[12] as String?,
      pageCount: fields[13] as int,
      uploadedBy: fields[14] as String,
      tags: (fields[15] as List).cast<String>(),
      rating: fields[16] as double?,
      downloadCount: fields[17] as int,
      lastAccessDate: fields[18] as DateTime?,
      isFavorite: fields[19] as bool,
      checksum: fields[20] as String?,
    );
  }

  @override
  void write(BinaryWriter writer, PdfFile obj) {
    writer
      ..writeByte(21)
      ..writeByte(0)
      ..write(obj.id)
      ..writeByte(1)
      ..write(obj.name)
      ..writeByte(2)
      ..write(obj.subject)
      ..writeByte(3)
      ..write(obj.category)
      ..writeByte(4)
      ..write(obj.description)
      ..writeByte(5)
      ..write(obj.onlineUrl)
      ..writeByte(6)
      ..write(obj.localPath)
      ..writeByte(7)
      ..write(obj.fileSize)
      ..writeByte(8)
      ..write(obj.uploadDate)
      ..writeByte(9)
      ..write(obj.downloadDate)
      ..writeByte(10)
      ..write(obj.isDownloaded)
      ..writeByte(11)
      ..write(obj.isAvailableOffline)
      ..writeByte(12)
      ..write(obj.thumbnailPath)
      ..writeByte(13)
      ..write(obj.pageCount)
      ..writeByte(14)
      ..write(obj.uploadedBy)
      ..writeByte(15)
      ..write(obj.tags)
      ..writeByte(16)
      ..write(obj.rating)
      ..writeByte(17)
      ..write(obj.downloadCount)
      ..writeByte(18)
      ..write(obj.lastAccessDate)
      ..writeByte(19)
      ..write(obj.isFavorite)
      ..writeByte(20)
      ..write(obj.checksum);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PdfFileAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
