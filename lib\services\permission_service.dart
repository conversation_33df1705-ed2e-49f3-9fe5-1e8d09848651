import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:permission_handler/permission_handler.dart';

/// خدمة إدارة الأذونات
class PermissionService {
  static final PermissionService _instance = PermissionService._internal();
  factory PermissionService() => _instance;
  PermissionService._internal();

  /// طلب أذونات التخزين
  Future<bool> requestStoragePermissions() async {
    try {
      if (kDebugMode) {
        print('🔐 طلب أذونات التخزين...');
      }

      // للأندرويد فقط
      if (!Platform.isAndroid) {
        if (kDebugMode) {
          print('✅ ليس أندرويد - لا حاجة لأذونات');
        }
        return true;
      }

      // التحقق من إصدار الأندرويد
      final androidInfo = await _getAndroidVersion();

      List<Permission> permissions = [];

      if (androidInfo >= 30) {
        // Android 11+ (API 30+)
        permissions.add(Permission.manageExternalStorage);
      } else {
        // Android 10 وأقل
        permissions.addAll([
          Permission.storage,
          Permission.accessMediaLocation,
        ]);
      }

      // طلب الأذونات
      Map<Permission, PermissionStatus> statuses = await permissions.request();

      // التحقق من النتائج
      bool allGranted = true;
      for (var entry in statuses.entries) {
        if (kDebugMode) {
          print('🔐 ${entry.key}: ${entry.value}');
        }

        if (entry.value != PermissionStatus.granted) {
          allGranted = false;
        }
      }

      if (kDebugMode) {
        print(
          allGranted ? '✅ تم منح جميع الأذونات' : '❌ لم يتم منح بعض الأذونات',
        );
      }

      return allGranted;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في طلب الأذونات: $e');
      }
      return false;
    }
  }

  /// التحقق من حالة أذونات التخزين
  Future<bool> checkStoragePermissions() async {
    try {
      if (!Platform.isAndroid) {
        return true;
      }

      final androidInfo = await _getAndroidVersion();

      if (androidInfo >= 30) {
        // Android 11+
        return await Permission.manageExternalStorage.isGranted;
      } else {
        // Android 10 وأقل
        final storageStatus = await Permission.storage.status;
        return storageStatus == PermissionStatus.granted;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في التحقق من الأذونات: $e');
      }
      return false;
    }
  }

  /// فتح إعدادات التطبيق
  Future<bool> openSettings() async {
    try {
      // فتح إعدادات التطبيق باستخدام permission_handler
      final opened = await openAppSettings();
      if (kDebugMode) {
        print(opened ? '✅ تم فتح الإعدادات' : '❌ فشل في فتح الإعدادات');
      }
      return opened;
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في فتح الإعدادات: $e');
      }
      return false;
    }
  }

  /// الحصول على إصدار الأندرويد
  Future<int> _getAndroidVersion() async {
    try {
      // يمكن استخدام device_info_plus للحصول على معلومات أكثر دقة
      // لكن هذا يكفي للآن
      return 30; // افتراض Android 11+ للأمان
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في الحصول على إصدار الأندرويد: $e');
      }
      return 30; // افتراض Android 11+ للأمان
    }
  }

  /// رسالة توضيحية للمستخدم حول الأذونات
  String getPermissionMessage() {
    return '''
يحتاج التطبيق إلى أذونات التخزين لتحميل ملفات PDF وحفظها على جهازك.

الأذونات المطلوبة:
• قراءة الملفات من التخزين
• كتابة الملفات في التخزين
• إدارة الملفات الخارجية

هذه الأذونات ضرورية لتحميل وحفظ ملفات PDF للوصول إليها بدون إنترنت.
    ''';
  }
}
