import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import '../models/pdf_model.dart';
import '../models/notification_model.dart';

class NotificationService {
  static final FirebaseMessaging _messaging = FirebaseMessaging.instance;
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();

  static const String notificationsCollection = 'notifications';
  static const String userTokensCollection = 'user_tokens';

  /// تهيئة خدمة الإشعارات
  static Future<void> initialize() async {
    try {
      // طلب أذونات الإشعارات
      await _requestPermissions();

      // تهيئة الإشعارات المحلية
      await _initializeLocalNotifications();

      // الحصول على FCM Token وحفظه
      await _saveUserToken();

      // معالجة الإشعارات
      _setupNotificationHandlers();

      if (kDebugMode) print('✅ تم تهيئة خدمة الإشعارات بنجاح');
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في تهيئة خدمة الإشعارات: $e');
    }
  }

  /// طلب أذونات الإشعارات
  static Future<void> _requestPermissions() async {
    final settings = await _messaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );

    if (kDebugMode) {
      print('إذن الإشعارات: ${settings.authorizationStatus}');
    }
  }

  /// تهيئة الإشعارات المحلية
  static Future<void> _initializeLocalNotifications() async {
    const androidSettings = AndroidInitializationSettings(
      '@mipmap/ic_launcher',
    );
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // إنشاء قناة إشعارات للأندرويد
    const androidChannel = AndroidNotificationChannel(
      'pdf_updates',
      'تحديثات PDF',
      description: 'إشعارات عند إضافة أو تحديث ملفات PDF',
      importance: Importance.high,
      sound: RawResourceAndroidNotificationSound('notification'),
    );

    await _localNotifications
        .resolvePlatformSpecificImplementation<
          AndroidFlutterLocalNotificationsPlugin
        >()
        ?.createNotificationChannel(androidChannel);
  }

  /// حفظ FCM Token للمستخدم
  static Future<void> _saveUserToken() async {
    try {
      final token = await _messaging.getToken();
      if (token != null) {
        await _firestore.collection(userTokensCollection).doc(token).set({
          'token': token,
          'platform': defaultTargetPlatform.name,
          'lastUpdated': FieldValue.serverTimestamp(),
          'isActive': true,
        });

        if (kDebugMode) {
          print('✅ تم حفظ FCM Token: ${token.substring(0, 20)}...');
        }
      }
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في حفظ FCM Token: $e');
    }
  }

  /// إعداد معالجات الإشعارات
  static void _setupNotificationHandlers() {
    // معالجة الإشعارات عندما يكون التطبيق مفتوحاً
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      _showLocalNotification(message);
    });

    // معالجة النقر على الإشعارات
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      _handleNotificationTap(message);
    });

    // معالجة الإشعارات في الخلفية
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
  }

  /// عرض إشعار محلي
  static Future<void> _showLocalNotification(RemoteMessage message) async {
    try {
      const androidDetails = AndroidNotificationDetails(
        'pdf_updates',
        'تحديثات PDF',
        channelDescription: 'إشعارات عند إضافة أو تحديث ملفات PDF',
        importance: Importance.high,
        priority: Priority.high,
        icon: '@mipmap/ic_launcher',
        sound: RawResourceAndroidNotificationSound('notification'),
        enableVibration: true,
        playSound: true,
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
        sound: 'notification.aiff',
      );

      const details = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _localNotifications.show(
        DateTime.now().millisecondsSinceEpoch ~/ 1000,
        message.notification?.title ?? 'تحديث جديد',
        message.notification?.body ?? 'تم إضافة محتوى جديد',
        details,
        payload: message.data.toString(),
      );

      // تشغيل اهتزاز خفيف
      HapticFeedback.lightImpact();

      if (kDebugMode) print('✅ تم عرض الإشعار المحلي');
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في عرض الإشعار المحلي: $e');
    }
  }

  /// معالجة النقر على الإشعار
  static void _onNotificationTapped(NotificationResponse response) {
    if (kDebugMode) print('تم النقر على الإشعار: ${response.payload}');
    // يمكن إضافة منطق التنقل هنا
  }

  /// معالجة النقر على إشعار Firebase
  static void _handleNotificationTap(RemoteMessage message) {
    if (kDebugMode) print('تم النقر على إشعار Firebase: ${message.data}');
    // يمكن إضافة منطق التنقل هنا
  }

  /// إرسال إشعار عن PDF جديد
  static Future<void> sendPDFNotification({
    required String title,
    required String body,
    required String yearName,
    required String subjectName,
    required String pdfName,
    required String action, // 'added', 'updated', 'deleted'
  }) async {
    try {
      // حفظ الإشعار في قاعدة البيانات
      await _firestore.collection(notificationsCollection).add({
        'title': title,
        'body': body,
        'yearName': yearName,
        'subjectName': subjectName,
        'pdfName': pdfName,
        'action': action,
        'timestamp': FieldValue.serverTimestamp(),
        'type': 'pdf_update',
        'isRead': false,
      });

      // إرسال إشعار فوري لجميع المستخدمين
      await _sendToAllUsers(title, body, {
        'type': 'pdf_update',
        'action': action,
        'yearName': yearName,
        'subjectName': subjectName,
        'pdfName': pdfName,
      });

      if (kDebugMode) print('✅ تم إرسال إشعار PDF: $title');
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في إرسال إشعار PDF: $e');
    }
  }

  /// إرسال إشعار لجميع المستخدمين
  static Future<void> _sendToAllUsers(
    String title,
    String body,
    Map<String, dynamic> data,
  ) async {
    try {
      // الحصول على جميع tokens المستخدمين النشطين
      final tokensSnapshot =
          await _firestore
              .collection(userTokensCollection)
              .where('isActive', isEqualTo: true)
              .get();

      final tokens =
          tokensSnapshot.docs
              .map((doc) => doc.data()['token'] as String)
              .toList();

      if (tokens.isEmpty) {
        if (kDebugMode) print('لا توجد tokens للإرسال إليها');
        return;
      }

      // إرسال إشعار جماعي (يحتاج Cloud Functions)
      // للآن سنعرض في الكونسول
      if (kDebugMode) {
        print('🔔 إشعار جماعي:');
        print('العنوان: $title');
        print('المحتوى: $body');
        print('عدد المستقبلين: ${tokens.length}');
        print('البيانات: $data');
      }

      // يمكن إضافة منطق إرسال حقيقي هنا باستخدام Cloud Functions
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في الإرسال الجماعي: $e');
    }
  }

  /// الحصول على الإشعارات للمستخدم
  static Stream<QuerySnapshot> getNotificationsStream() {
    return _firestore
        .collection(notificationsCollection)
        .orderBy('timestamp', descending: true)
        .limit(50)
        .snapshots();
  }

  /// تحديد الإشعار كمقروء
  static Future<void> markAsRead(String notificationId) async {
    try {
      await _firestore
          .collection(notificationsCollection)
          .doc(notificationId)
          .update({'isRead': true});
    } catch (e) {
      if (kDebugMode) print('خطأ في تحديد الإشعار كمقروء: $e');
    }
  }

  /// حذف الإشعار
  static Future<void> deleteNotification(String notificationId) async {
    try {
      await _firestore
          .collection(notificationsCollection)
          .doc(notificationId)
          .delete();
    } catch (e) {
      if (kDebugMode) print('خطأ في حذف الإشعار: $e');
    }
  }

  /// إرسال إشعار عند إضافة ملف PDF جديد
  static Future<void> sendNewPDFNotification(PDFModel pdf) async {
    try {
      final notificationData = {
        'id': DateTime.now().millisecondsSinceEpoch.toString(),
        'title': 'ملف جديد متاح 📄',
        'body': 'تم إضافة ملف جديد في ${pdf.category} - ${pdf.subjectName}',
        'type': 'new_pdf',
        'pdfId': pdf.id,
        'subjectId': pdf.subjectId,
        'category': pdf.category,
        'createdAt': FieldValue.serverTimestamp(),
        'isRead': false,
        'data': {
          'pdfName': pdf.name,
          'subjectName': pdf.subjectName,
          'category': pdf.category,
          'url': pdf.url,
        },
      };

      // حفظ الإشعار في قاعدة البيانات
      await _firestore
          .collection(notificationsCollection)
          .doc(notificationData['id'] as String)
          .set(notificationData);

      // إرسال الإشعار المحلي
      await _localNotifications.show(
        DateTime.now().millisecondsSinceEpoch ~/ 1000,
        notificationData['title'] as String,
        notificationData['body'] as String,
        const NotificationDetails(
          android: AndroidNotificationDetails(
            'pdf_updates',
            'تحديثات PDF',
            channelDescription: 'إشعارات عند إضافة أو تحديث ملفات PDF',
            importance: Importance.high,
            priority: Priority.high,
            icon: '@mipmap/ic_launcher',
            color: Color(0xFF10B981),
          ),
        ),
        payload: pdf.id,
      );

      if (kDebugMode) {
        print('✅ تم إرسال إشعار ملف PDF جديد: ${pdf.name}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في إرسال إشعار PDF: $e');
      }
    }
  }

  /// الحصول على عدد الإشعارات غير المقروءة
  static Stream<int> getUnreadNotificationsCount() {
    return _firestore
        .collection(notificationsCollection)
        .where('isRead', isEqualTo: false)
        .snapshots()
        .map((snapshot) => snapshot.docs.length);
  }

  /// تمييز جميع الإشعارات كمقروءة
  static Future<void> markAllAsRead() async {
    try {
      final batch = _firestore.batch();
      final snapshot =
          await _firestore
              .collection(notificationsCollection)
              .where('isRead', isEqualTo: false)
              .get();

      for (var doc in snapshot.docs) {
        batch.update(doc.reference, {'isRead': true});
      }

      await batch.commit();

      if (kDebugMode) {
        print('✅ تم تمييز ${snapshot.docs.length} إشعار كمقروء');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ خطأ في تمييز جميع الإشعارات كمقروءة: $e');
      }
    }
  }

  /// إرسال إشعار ملف جديد لفرقة دراسية محددة
  static Future<void> sendNewFileNotification({
    required String fileName,
    required String subjectName,
    required String academicYear,
    required String category,
    String? fileUrl,
  }) async {
    try {
      if (kDebugMode) {
        print('📤 إرسال إشعار ملف جديد: $fileName للفرقة: $academicYear');
      }

      // الحصول على جميع المستخدمين في نفس الفرقة الدراسية
      final usersQuery =
          await _firestore
              .collection('users')
              .where('academicYear', isEqualTo: academicYear)
              .get();

      if (usersQuery.docs.isEmpty) {
        if (kDebugMode) print('⚠️ لا يوجد مستخدمون في الفرقة: $academicYear');
        return;
      }

      // إنشاء الإشعار لكل مستخدم
      final batch = _firestore.batch();
      final notificationId = DateTime.now().millisecondsSinceEpoch.toString();

      for (final userDoc in usersQuery.docs) {
        final notification = NotificationModel.newFile(
          userId: userDoc.id,
          fileName: fileName,
          subjectName: subjectName,
          academicYear: academicYear,
          category: category,
          fileUrl: fileUrl,
        );

        final notificationRef = _firestore
            .collection(notificationsCollection)
            .doc('${userDoc.id}_$notificationId');

        batch.set(notificationRef, notification.toFirestore());
      }

      // حفظ الإشعارات في قاعدة البيانات
      await batch.commit();

      // تنظيف الإشعارات القديمة
      await _cleanupOldNotifications();

      if (kDebugMode) {
        print('✅ تم إرسال الإشعار بنجاح لـ ${usersQuery.docs.length} مستخدم');
      }
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في إرسال إشعار الملف الجديد: $e');
    }
  }

  /// تنظيف الإشعارات القديمة (الاحتفاظ بآخر 5 فقط)
  static Future<void> _cleanupOldNotifications() async {
    try {
      // الحصول على جميع المستخدمين
      final usersQuery = await _firestore.collection('users').get();

      for (final userDoc in usersQuery.docs) {
        final userId = userDoc.id;

        // الحصول على إشعارات المستخدم مرتبة حسب التاريخ
        final notificationsQuery =
            await _firestore
                .collection(notificationsCollection)
                .where('userId', isEqualTo: userId)
                .orderBy('createdAt', descending: true)
                .get();

        // إذا كان عدد الإشعارات أكثر من 5، احذف القديمة
        if (notificationsQuery.docs.length > 5) {
          final batch = _firestore.batch();
          final docsToDelete = notificationsQuery.docs.skip(5);

          for (final doc in docsToDelete) {
            batch.delete(doc.reference);
          }

          await batch.commit();
          if (kDebugMode) {
            print(
              '🗑️ تم حذف ${docsToDelete.length} إشعار قديم للمستخدم: $userId',
            );
          }
        }
      }
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في تنظيف الإشعارات القديمة: $e');
    }
  }

  // ===== دوال المجتمع =====

  /// إنشاء إشعار إعجاب للمجتمع
  static Future<bool> createCommunityLikeNotification({
    required String postOwnerId,
    required String postId,
    required String postContent,
    required String likerName,
    required String likerId,
  }) async {
    try {
      // لا نرسل إشعار للمستخدم نفسه
      if (postOwnerId == likerId) return true;

      // قطع محتوى المنشور إلى 50 حرف
      final shortContent =
          postContent.length > 50
              ? '${postContent.substring(0, 50)}...'
              : postContent;

      final notification = NotificationModel(
        id: '',
        userId: postOwnerId,
        title: 'إعجاب جديد',
        body: 'أعجب $likerName بمنشورك: "$shortContent"',
        type: NotificationType.like,
        data: {
          'postId': postId,
          'actorId': likerId,
          'actorName': likerName,
          'postContent': shortContent,
        },
        createdAt: DateTime.now(),
      );

      await _firestore
          .collection(notificationsCollection)
          .add(notification.toFirestore());

      if (kDebugMode) print('✅ تم إنشاء إشعار إعجاب');
      return true;
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في إنشاء إشعار الإعجاب: $e');
      return false;
    }
  }

  /// إنشاء إشعار تعليق للمجتمع
  static Future<bool> createCommunityCommentNotification({
    required String postOwnerId,
    required String postId,
    required String postContent,
    required String commenterName,
    required String commenterId,
  }) async {
    try {
      // لا نرسل إشعار للمستخدم نفسه
      if (postOwnerId == commenterId) return true;

      // قطع محتوى المنشور إلى 50 حرف
      final shortContent =
          postContent.length > 50
              ? '${postContent.substring(0, 50)}...'
              : postContent;

      final notification = NotificationModel(
        id: '',
        userId: postOwnerId,
        title: 'تعليق جديد',
        body: 'علق $commenterName على منشورك: "$shortContent"',
        type: NotificationType.comment,
        data: {
          'postId': postId,
          'actorId': commenterId,
          'actorName': commenterName,
          'postContent': shortContent,
        },
        createdAt: DateTime.now(),
      );

      await _firestore
          .collection(notificationsCollection)
          .add(notification.toFirestore());

      if (kDebugMode) print('✅ تم إنشاء إشعار تعليق');
      return true;
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في إنشاء إشعار التعليق: $e');
      return false;
    }
  }

  /// الحصول على إشعارات المجتمع للمستخدم (آخر 10)
  static Stream<List<NotificationModel>> getCommunityNotificationsStream(
    String userId,
  ) {
    try {
      return _firestore
          .collection(notificationsCollection)
          .where('userId', isEqualTo: userId)
          .where('type', whereIn: ['like', 'comment'])
          .orderBy('createdAt', descending: true)
          .limit(10)
          .snapshots()
          .map((snapshot) {
            return snapshot.docs.map((doc) {
              return NotificationModel.fromFirestore(doc);
            }).toList();
          });
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في جلب إشعارات المجتمع: $e');
      return Stream.value([]);
    }
  }

  /// الحصول على عدد الإشعارات غير المقروءة للمجتمع
  static Stream<int> getCommunityUnreadCount(String userId) {
    try {
      return _firestore
          .collection(notificationsCollection)
          .where('userId', isEqualTo: userId)
          .where('type', whereIn: ['like', 'comment'])
          .where('isRead', isEqualTo: false)
          .snapshots()
          .map((snapshot) => snapshot.docs.length);
    } catch (e) {
      if (kDebugMode) print('❌ خطأ في جلب عدد الإشعارات غير المقروءة: $e');
      return Stream.value(0);
    }
  }
}

/// معالج الإشعارات في الخلفية
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  if (kDebugMode) {
    print('معالجة إشعار في الخلفية: ${message.messageId}');
  }
}
