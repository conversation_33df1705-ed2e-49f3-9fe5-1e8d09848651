# 🔧 دليل إصلاح مشكلة إرسال كود التحقق - Legal 2025

## 🚨 **المشكلة الحالية**

حالياً، النظام **لا يرسل** كود التحقق عبر البريد الإلكتروني، بل يعرضه في صفحة إدخال الكود أو في وحدة التحكم.

## 🔍 **سبب المشكلة**

1. **إعدادات Firebase Functions فارغة** - لا توجد إعدادات Gmail
2. **مفاتيح EmailJS وهمية** - غير مُكونة بشكل صحيح
3. **رابط Formspree غير صحيح** - يحتاج إعداد
4. **كلمة مرور Gmail وهمية** - في EmailConfig

## 🛠️ **الحلول المتاحة (اختر واحد)**

### **الحل الأول: Gmail SMTP (الأسرع) ⭐**

#### **الخطوة 1: إعداد Gmail**
1. **إنشاء حساب Gmail جديد** أو استخدام حساب موجود:
   ```
   مثال: <EMAIL>
   ```

2. **تفعيل التحقق بخطوتين**:
   - اذهب إلى: https://myaccount.google.com/security
   - فعّل "2-Step Verification"

3. **إنشاء كلمة مرور التطبيق**:
   - في نفس الصفحة، اختر "App passwords"
   - اختر "Mail" كنوع التطبيق
   - انسخ كلمة المرور (16 حرف)

#### **الخطوة 2: تحديث الإعدادات**
افتح `lib/config/email_config.dart` وحدث:

```dart
class EmailConfig {
  // غير هذا إلى بريدك الإلكتروني الحقيقي
  static const String senderEmail = '<EMAIL>';
  static const String senderName = 'تطبيق Legal 2025';
  
  // غير هذا إلى كلمة مرور التطبيق الحقيقية
  static const String appPassword = 'abcd efgh ijkl mnop'; // كلمة المرور الحقيقية
}
```

---

### **الحل الثاني: Formspree (مجاني وسهل) ⭐**

#### **الخطوة 1: إنشاء حساب Formspree**
1. اذهب إلى: https://formspree.io
2. أنشئ حساب مجاني
3. أنشئ نموذج جديد
4. انسخ الـ endpoint (مثل: `https://formspree.io/f/xabc1234`)

#### **الخطوة 2: تحديث الإعدادات**
افتح `lib/services/real_email_service.dart` وحدث:

```dart
// إعدادات Formspree (بديل سهل)
static const String _formspreeEndpoint = 'https://formspree.io/f/xabc1234'; // الرابط الحقيقي
```

---

### **الحل الثالث: Firebase Functions (متقدم)**

#### **الخطوة 1: تكوين إعدادات Firebase**
```bash
# في terminal
firebase functions:config:set gmail.email="<EMAIL>"
firebase functions:config:set gmail.password="your_app_password_here"
```

#### **الخطوة 2: نشر Functions**
```bash
firebase deploy --only functions
```

---

### **الحل الرابع: EmailJS (بديل)**

#### **الخطوة 1: إنشاء حساب EmailJS**
1. اذهب إلى: https://www.emailjs.com
2. أنشئ حساب مجاني
3. أنشئ خدمة بريد إلكتروني
4. أنشئ قالب للرسائل
5. احصل على المفاتيح

#### **الخطوة 2: تحديث الإعدادات**
افتح `lib/services/real_email_service.dart` وحدث:

```dart
// إعدادات EmailJS (خدمة مجانية) - مُعدة للعمل
static const String _serviceId = 'service_abc123';      // الخدمة الحقيقية
static const String _templateId = 'template_xyz789';    // القالب الحقيقي
static const String _publicKey = 'user_123456';         // المفتاح العام
static const String _privateKey = 'private_789012';     // المفتاح الخاص
```

## 🚀 **التوصية**

**أنصح بالحل الأول (Gmail SMTP)** لأنه:
- ✅ سريع الإعداد (5 دقائق)
- ✅ مجاني تماماً
- ✅ موثوق وسريع
- ✅ لا يحتاج خدمات خارجية

## 🧪 **اختبار الحل**

بعد تطبيق أي حل:

1. **شغل التطبيق**
2. **جرب التسجيل** ببريد إلكتروني حقيقي
3. **تحقق من البريد الإلكتروني** (تحقق من مجلد Spam أيضاً)
4. **أدخل الكود** في التطبيق

## ⚠️ **ملاحظات مهمة**

1. **لا تشارك كلمة مرور التطبيق** مع أحد
2. **في الإنتاج، استخدم متغيرات البيئة** لحفظ كلمات المرور
3. **تأكد من عدم رفع كلمات المرور إلى Git**
4. **اختبر الحل قبل النشر**

## 🆘 **إذا لم يعمل أي حل**

1. تحقق من اتصال الإنترنت
2. تحقق من إعدادات الأمان في Gmail
3. تحقق من صحة البريد الإلكتروني المُدخل
4. راجع logs التطبيق للأخطاء
5. جرب بريد إلكتروني مختلف

---

**بعد تطبيق أي حل، ستحصل على كود التحقق في بريدك الإلكتروني بدلاً من عرضه في التطبيق! 📧✅**
