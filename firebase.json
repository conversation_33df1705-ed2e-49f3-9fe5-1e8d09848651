{"hosting": {"public": "build/web", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}], "headers": [{"source": "**/*.@(js|html)", "headers": [{"key": "Cache-Control", "value": "max-age=0"}]}]}, "firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "storage": {"rules": "storage.rules"}, "functions": {"source": "functions", "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run lint"], "runtime": "nodejs18"}}