{"name": "legal2025-functions", "description": "Firebase Functions for Legal 2025 App", "scripts": {"lint": "eslint .", "serve": "firebase emulators:start --only functions", "shell": "firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "18"}, "main": "index.js", "dependencies": {"firebase-admin": "^12.0.0", "firebase-functions": "^4.8.0", "nodemailer": "^6.9.8"}, "devDependencies": {"eslint": "^8.15.0", "eslint-config-google": "^0.14.0", "firebase-functions-test": "^3.1.0"}, "private": true}