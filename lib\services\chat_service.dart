import 'dart:async';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_database/firebase_database.dart';
import '../models/chat_model.dart';
import '../utils/logger.dart';

/// خدمة المحادثة الفورية باستخدام Firebase Realtime Database
class ChatService {
  static final FirebaseDatabase _database = FirebaseDatabase.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  // المراجع الأساسية
  static DatabaseReference get _chatRoomsRef => _database.ref('chatRooms');
  static DatabaseReference get _chatsRef => _database.ref('chats');
  static DatabaseReference get _presenceRef => _database.ref('presence');
  static DatabaseReference get _typingRef => _database.ref('typing');

  // تهيئة غرف المحادثة الافتراضية
  static Future<void> initializeDefaultChatRooms() async {
    try {
      // إنشاء الغرفة العامة
      await _chatRoomsRef.child('general').set({
        'name': 'المحادثة العامة',
        'description': 'محادثة عامة لجميع الطلاب',
        'academicYear': 'عام',
        'isGeneral': true,
        'createdAt': DateTime.now().millisecondsSinceEpoch,
      });

      // إنشاء غرف السنوات الدراسية
      final years = [
        'السنة الأولى',
        'السنة الثانية',
        'السنة الثالثة',
        'السنة الرابعة',
      ];
      for (int i = 0; i < years.length; i++) {
        final yearId = 'year_${i + 1}';
        await _chatRoomsRef.child(yearId).set({
          'name': years[i],
          'description': 'محادثة طلاب ${years[i]}',
          'academicYear': years[i],
          'isGeneral': false,
          'createdAt': DateTime.now().millisecondsSinceEpoch,
        });
      }
    } catch (e) {
      AppLogger.error('خطأ في تهيئة غرف المحادثة', 'ChatService', e);
    }
  }

  // الحصول على تدفق غرف المحادثة
  static Stream<List<ChatRoomModel>> getChatRoomsStream() {
    return _chatRoomsRef.onValue.map((event) {
      final List<ChatRoomModel> chatRooms = [];

      if (event.snapshot.value != null) {
        final data = Map<String, dynamic>.from(event.snapshot.value as Map);

        data.forEach((key, value) {
          if (value != null) {
            final roomData = Map<dynamic, dynamic>.from(value);
            chatRooms.add(ChatRoomModel.fromRealtimeDatabase(key, roomData));
          }
        });
      }

      // ترتيب الغرف: العامة أولاً ثم السنوات
      chatRooms.sort((a, b) {
        if (a.isGeneral && !b.isGeneral) return -1;
        if (!a.isGeneral && b.isGeneral) return 1;
        return a.name.compareTo(b.name);
      });

      return chatRooms;
    });
  }

  // الانضمام إلى غرفة محادثة
  static Future<bool> joinChatRoom(String chatRoomId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      await _chatRoomsRef
          .child(chatRoomId)
          .child('members')
          .child(user.uid)
          .set(true);

      return true;
    } catch (e) {
      AppLogger.error('خطأ في الانضمام للغرفة', 'ChatService', e);
      return false;
    }
  }

  // مغادرة غرفة محادثة
  static Future<bool> leaveChatRoom(String chatRoomId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      await _chatRoomsRef
          .child(chatRoomId)
          .child('members')
          .child(user.uid)
          .remove();

      return true;
    } catch (e) {
      AppLogger.error('خطأ في مغادرة الغرفة', 'ChatService', e);
      return false;
    }
  }

  // التحقق من عضوية المستخدم في الغرفة
  static Future<bool> isUserMember(String chatRoomId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      final snapshot =
          await _chatRoomsRef
              .child(chatRoomId)
              .child('members')
              .child(user.uid)
              .get();

      return snapshot.exists;
    } catch (e) {
      AppLogger.error('خطأ في التحقق من العضوية', 'ChatService', e);
      return false;
    }
  }

  // إرسال رسالة مع إدارة الحد الأقصى للرسائل
  static Future<bool> sendMessage({
    required String chatRoomId,
    required String message,
    String? imageUrl,
    String? fileUrl,
    String? fileName,
    MessageType type = MessageType.text,
  }) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      // التحقق من العضوية أو إذا كانت الغرفة عامة
      final roomSnapshot = await _chatRoomsRef.child(chatRoomId).get();
      if (!roomSnapshot.exists) return false;

      final roomData = Map<dynamic, dynamic>.from(roomSnapshot.value as Map);
      final isGeneral = roomData['isGeneral'] == true;

      if (!isGeneral) {
        final isMember = await isUserMember(chatRoomId);
        if (!isMember) return false;
      }

      // إنشاء الرسالة
      final messageRef = _chatsRef.child(chatRoomId).child('messages').push();
      final messageData = {
        'senderId': user.uid,
        'senderName': user.displayName ?? 'مستخدم',
        'message': message,
        'timestamp': DateTime.now().millisecondsSinceEpoch,
        'type': type.toString().split('.').last,
        if (imageUrl != null) 'imageUrl': imageUrl,
        if (fileUrl != null) 'fileUrl': fileUrl,
        if (fileName != null) 'fileName': fileName,
      };

      await messageRef.set(messageData);

      // تحديث آخر رسالة في الغرفة
      await _chatRoomsRef.child(chatRoomId).update({
        'lastMessage': message,
        'lastMessageTime': DateTime.now().millisecondsSinceEpoch,
      });

      // إزالة مؤشر الكتابة
      await _typingRef.child(chatRoomId).child(user.uid).remove();

      // إدارة الحد الأقصى للرسائل (150 رسالة)
      await _manageMessageLimit(chatRoomId);

      return true;
    } catch (e) {
      AppLogger.error('خطأ في إرسال الرسالة', 'ChatService', e);
      return false;
    }
  }

  // حذف رسالة
  static Future<bool> deleteMessage(String chatRoomId, String messageId) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return false;

      // التحقق من أن المستخدم هو صاحب الرسالة
      final messageRef = _chatsRef
          .child(chatRoomId)
          .child('messages')
          .child(messageId);
      final messageSnapshot = await messageRef.once();

      if (messageSnapshot.snapshot.value != null) {
        final messageData = Map<String, dynamic>.from(
          messageSnapshot.snapshot.value as Map,
        );
        final senderId = messageData['senderId'];

        // التحقق من الصلاحية
        if (senderId == user.uid) {
          // حذف الرسالة
          await messageRef.remove();

          AppLogger.info('تم حذف الرسالة بنجاح', 'ChatService');
          return true;
        } else {
          AppLogger.warning(
            'المستخدم غير مخول لحذف هذه الرسالة',
            'ChatService',
          );
          return false;
        }
      }

      return false;
    } catch (e) {
      AppLogger.error('خطأ في حذف الرسالة', 'ChatService', e);
      return false;
    }
  }

  // إدارة الحد الأقصى للرسائل (200 رسالة)
  static Future<void> _manageMessageLimit(String chatRoomId) async {
    try {
      final messagesRef = _chatsRef.child(chatRoomId).child('messages');

      // الحصول على جميع الرسائل مرتبة حسب الوقت
      final snapshot = await messagesRef.orderByChild('timestamp').once();

      if (snapshot.snapshot.value != null) {
        final data = Map<String, dynamic>.from(snapshot.snapshot.value as Map);

        // إذا تجاوز عدد الرسائل 200، احذف الرسائل القديمة
        if (data.length > 150) {
          // ترتيب المفاتيح حسب الوقت
          final sortedEntries =
              data.entries.toList()..sort((a, b) {
                final timestampA = a.value['timestamp'] ?? 0;
                final timestampB = b.value['timestamp'] ?? 0;
                return timestampA.compareTo(timestampB);
              });

          // حذف الرسائل الزائدة (الأقدم)
          final messagesToDelete = data.length - 150;
          for (int i = 0; i < messagesToDelete; i++) {
            await messagesRef.child(sortedEntries[i].key).remove();
            AppLogger.info(
              '🗑️ تم حذف رسالة قديمة من $chatRoomId',
              'ChatService',
            );
          }
        }
      }
    } catch (e) {
      AppLogger.error('خطأ في إدارة حد الرسائل', 'ChatService', e);
    }
  }

  // الحصول على الرسائل الأولية (آخر 10 رسائل فقط)
  static Future<List<MessageModel>> getInitialMessages(
    String chatRoomId, {
    int limit = 10,
  }) async {
    try {
      final snapshot =
          await _chatsRef
              .child(chatRoomId)
              .child('messages')
              .orderByChild('timestamp')
              .limitToLast(limit)
              .get();

      final List<MessageModel> messages = [];

      if (snapshot.exists && snapshot.value != null) {
        final data = Map<String, dynamic>.from(snapshot.value as Map);

        data.forEach((key, value) {
          if (value != null) {
            final messageData = Map<dynamic, dynamic>.from(value);
            messages.add(MessageModel.fromRealtimeDatabase(key, messageData));
          }
        });
      }

      // ترتيب الرسائل حسب الوقت (الأحدث أولاً)
      messages.sort((a, b) => b.timestamp!.compareTo(a.timestamp!));

      return messages;
    } catch (e) {
      AppLogger.error('خطأ في تحميل الرسائل الأولية', 'ChatService', e);
      return [];
    }
  }

  // الحصول على تدفق الرسائل الجديدة فقط
  static Stream<List<MessageModel>> getNewMessagesStream(String chatRoomId) {
    return _chatsRef
        .child(chatRoomId)
        .child('messages')
        .orderByChild('timestamp')
        .startAt(DateTime.now().millisecondsSinceEpoch)
        .onValue
        .map((event) {
          final List<MessageModel> messages = [];

          if (event.snapshot.value != null) {
            final data = Map<String, dynamic>.from(event.snapshot.value as Map);

            data.forEach((key, value) {
              if (value != null) {
                final messageData = Map<dynamic, dynamic>.from(value);
                messages.add(
                  MessageModel.fromRealtimeDatabase(key, messageData),
                );
              }
            });
          }

          // ترتيب الرسائل حسب الوقت (الأحدث أولاً)
          messages.sort((a, b) => b.timestamp!.compareTo(a.timestamp!));

          return messages;
        });
  }

  // تحميل رسائل أقدم (للتصفح للأعلى)
  static Future<List<MessageModel>> loadMoreMessages(
    String chatRoomId,
    DateTime beforeTimestamp, {
    int limit = 10,
  }) async {
    try {
      final snapshot =
          await _chatsRef
              .child(chatRoomId)
              .child('messages')
              .orderByChild('timestamp')
              .endBefore(beforeTimestamp.millisecondsSinceEpoch)
              .limitToLast(limit)
              .get();

      final List<MessageModel> messages = [];

      if (snapshot.exists && snapshot.value != null) {
        final data = Map<String, dynamic>.from(snapshot.value as Map);

        data.forEach((key, value) {
          if (value != null) {
            final messageData = Map<dynamic, dynamic>.from(value);
            messages.add(MessageModel.fromRealtimeDatabase(key, messageData));
          }
        });
      }

      // ترتيب الرسائل حسب الوقت (الأحدث أولاً)
      messages.sort((a, b) => b.timestamp!.compareTo(a.timestamp!));

      return messages;
    } catch (e) {
      AppLogger.error('خطأ في تحميل المزيد من الرسائل', 'ChatService', e);
      return [];
    }
  }

  // الحصول على تدفق الرسائل (للاستخدام القديم - مع تحسين)
  static Stream<List<MessageModel>> getMessagesStream(String chatRoomId) {
    return _chatsRef
        .child(chatRoomId)
        .child('messages')
        .orderByChild('timestamp')
        .limitToLast(50) // تقليل إلى 50 رسالة بدلاً من 200
        .onValue
        .map((event) {
          final List<MessageModel> messages = [];

          if (event.snapshot.value != null) {
            final data = Map<String, dynamic>.from(event.snapshot.value as Map);

            data.forEach((key, value) {
              if (value != null) {
                final messageData = Map<dynamic, dynamic>.from(value);
                messages.add(
                  MessageModel.fromRealtimeDatabase(key, messageData),
                );
              }
            });
          }

          // ترتيب الرسائل حسب الوقت (الأحدث أولاً)
          messages.sort((a, b) => b.timestamp!.compareTo(a.timestamp!));

          return messages;
        });
  }

  // تحديث حالة الحضور
  static Future<void> updatePresence(bool isOnline) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return;

      final presenceData = {
        'name': user.displayName ?? 'مستخدم',
        'isOnline': isOnline,
        'lastSeen': DateTime.now().millisecondsSinceEpoch,
      };

      await _presenceRef.child(user.uid).set(presenceData);

      // إعداد إزالة الحضور عند قطع الاتصال
      if (isOnline) {
        await _presenceRef.child(user.uid).onDisconnect().update({
          'isOnline': false,
          'lastSeen': DateTime.now().millisecondsSinceEpoch,
        });
      }
    } catch (e) {
      AppLogger.error('خطأ في تحديث الحضور', 'ChatService', e);
    }
  }

  // الحصول على تدفق المستخدمين المتصلين
  static Stream<List<ChatUser>> getOnlineUsersStream() {
    return _presenceRef.onValue.map((event) {
      final List<ChatUser> onlineUsers = [];

      if (event.snapshot.value != null) {
        final data = Map<String, dynamic>.from(event.snapshot.value as Map);

        data.forEach((key, value) {
          if (value != null) {
            final userData = Map<dynamic, dynamic>.from(value);
            if (userData['isOnline'] == true) {
              onlineUsers.add(ChatUser.fromRealtimeDatabase(key, userData));
            }
          }
        });
      }

      return onlineUsers;
    });
  }

  // إرسال مؤشر الكتابة
  static Future<void> sendTypingIndicator(
    String chatRoomId,
    bool isTyping,
  ) async {
    try {
      final user = _auth.currentUser;
      if (user == null) return;

      if (isTyping) {
        await _typingRef.child(chatRoomId).child(user.uid).set({
          'userName': user.displayName ?? 'مستخدم',
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        });
      } else {
        await _typingRef.child(chatRoomId).child(user.uid).remove();
      }
    } catch (e) {
      AppLogger.error('خطأ في إرسال مؤشر الكتابة', 'ChatService', e);
    }
  }

  // الحصول على تدفق مؤشرات الكتابة
  static Stream<List<TypingIndicator>> getTypingIndicatorsStream(
    String chatRoomId,
  ) {
    return _typingRef.child(chatRoomId).onValue.map((event) {
      final List<TypingIndicator> typingUsers = [];
      final currentUserId = _auth.currentUser?.uid;

      if (event.snapshot.value != null) {
        final data = Map<String, dynamic>.from(event.snapshot.value as Map);

        data.forEach((key, value) {
          if (value != null && key != currentUserId) {
            final typingData = Map<dynamic, dynamic>.from(value);
            final indicator = TypingIndicator.fromRealtimeDatabase(
              key,
              typingData,
            );

            // إضافة المؤشر فقط إذا لم ينته وقته
            if (!indicator.isExpired) {
              typingUsers.add(indicator);
            }
          }
        });
      }

      return typingUsers;
    });
  }

  // تنظيف مؤشرات الكتابة المنتهية الصلاحية
  static Future<void> cleanupExpiredTypingIndicators() async {
    try {
      final snapshot = await _typingRef.get();
      if (!snapshot.exists) return;

      final data = Map<String, dynamic>.from(snapshot.value as Map);
      final now = DateTime.now().millisecondsSinceEpoch;

      data.forEach((chatRoomId, chatData) async {
        if (chatData != null) {
          final chatTypingData = Map<String, dynamic>.from(chatData);

          chatTypingData.forEach((userId, userData) async {
            if (userData != null) {
              final userTypingData = Map<dynamic, dynamic>.from(userData);
              final timestamp = userTypingData['timestamp'] ?? 0;

              // إزالة المؤشرات الأقدم من 5 ثوان
              if (now - timestamp > 5000) {
                await _typingRef.child(chatRoomId).child(userId).remove();
              }
            }
          });
        }
      });
    } catch (e) {
      AppLogger.error('خطأ في تنظيف مؤشرات الكتابة', 'ChatService', e);
    }
  }
}
