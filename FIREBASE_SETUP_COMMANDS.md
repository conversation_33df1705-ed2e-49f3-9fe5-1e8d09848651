# 🔧 أوامر إعداد Firebase Functions - Legal 2025

## 📧 **إعداد Gmail للإرسال**

### **الخطوة 1: إعداد حساب Gmail**
1. إنشاء حساب Gmail جديد: `<EMAIL>`
2. تفعيل التحقق بخطوتين
3. إنشاء كلمة مرور التطبيق

### **الخطوة 2: تكوين Firebase Functions**

بعد الحصول على بيانات Gmail، قم بتشغيل الأوامر التالية:

```bash
# تكوين بريد Gmail
firebase functions:config:set gmail.email="<EMAIL>"

# تكوين كلمة مرور التطبيق (استبدل YOUR_APP_PASSWORD بكلمة المرور الحقيقية)
firebase functions:config:set gmail.password="YOUR_APP_PASSWORD"

# التحقق من الإعدادات
firebase functions:config:get

# نشر Functions
firebase deploy --only functions
```

### **الخطوة 3: اختبار الإعدادات**

```bash
# اختبار Functions محلياً
firebase emulators:start --only functions

# عرض logs
firebase functions:log
```

## 🔒 **أمان البيانات**

### **⚠️ تحذيرات مهمة:**
- لا تشارك كلمة مرور التطبيق مع أحد
- استخدم حساب Gmail منفصل للتطبيق
- في الإنتاج، استخدم متغيرات البيئة

### **🛡️ أفضل الممارسات:**
```bash
# للحماية الإضافية، يمكن استخدام Firebase Remote Config
firebase functions:config:set gmail.enabled="true"
firebase functions:config:set gmail.max_daily_emails="1000"
```

## 📝 **مثال على الإعدادات الصحيحة:**

```bash
# مثال (لا تستخدم هذه البيانات الحقيقية)
firebase functions:config:set gmail.email="<EMAIL>"
firebase functions:config:set gmail.password="abcd efgh ijkl mnop"
```

## 🧪 **اختبار الإعداد:**

بعد تكوين الإعدادات:

1. **نشر Functions**: `firebase deploy --only functions`
2. **اختبار التطبيق**: جرب التسجيل ببريد إلكتروني حقيقي
3. **تحقق من البريد**: ابحث عن رسالة كود التحقق
4. **مراجعة Logs**: `firebase functions:log` لمراقبة الأخطاء

## 🔄 **إذا لم يعمل:**

```bash
# إعادة تعيين الإعدادات
firebase functions:config:unset gmail

# إعادة التكوين
firebase functions:config:set gmail.email="<EMAIL>"
firebase functions:config:set gmail.password="your_app_password"

# إعادة النشر
firebase deploy --only functions
```

## 📊 **مراقبة الأداء:**

```bash
# عرض إحصائيات Functions
firebase functions:log --limit 50

# مراقبة الأخطاء
firebase functions:log --filter "ERROR"
```

---

**بعد تطبيق هذه الخطوات، ستحصل على كود التحقق في بريدك الإلكتروني بدلاً من عرضه في التطبيق! 📧✅**
