const functions = require('firebase-functions');
const admin = require('firebase-admin');
const nodemailer = require('nodemailer');

admin.initializeApp();

// إعدادات البريد الإلكتروني
const gmailEmail = functions.config().gmail.email;
const gmailPassword = functions.config().gmail.password;

// إنشاء transporter للبريد الإلكتروني
const mailTransporter = nodemailer.createTransporter({
  service: 'gmail',
  auth: {
    user: gmailEmail,
    pass: gmailPassword,
  },
});

// دالة إرسال كود التحقق
exports.sendVerificationCode = functions.https.onCall(async (data, context) => {
  try {
    const { email, code, displayName } = data;

    // التحقق من صحة البيانات
    if (!email || !code) {
      throw new functions.https.HttpsError('invalid-argument', 'البريد الإلكتروني والكود مطلوبان');
    }

    // إعداد محتوى الإيميل
    const mailOptions = {
      from: `Legal 2025 <${gmailEmail}>`,
      to: email,
      subject: 'كود التحقق - Legal 2025',
      html: `
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>كود التحقق - Legal 2025</title>
          <style>
            body {
              font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
              background-color: #f8fafc;
              margin: 0;
              padding: 20px;
              direction: rtl;
            }
            .container {
              max-width: 600px;
              margin: 0 auto;
              background-color: white;
              border-radius: 12px;
              box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
              overflow: hidden;
            }
            .header {
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              color: white;
              padding: 30px;
              text-align: center;
            }
            .header h1 {
              margin: 0;
              font-size: 28px;
              font-weight: bold;
            }
            .content {
              padding: 40px 30px;
              text-align: center;
            }
            .greeting {
              font-size: 18px;
              color: #374151;
              margin-bottom: 20px;
            }
            .code-container {
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              color: white;
              padding: 25px;
              border-radius: 12px;
              margin: 30px 0;
              box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            }
            .code {
              font-size: 36px;
              font-weight: bold;
              letter-spacing: 8px;
              margin: 10px 0;
              font-family: 'Courier New', monospace;
            }
            .code-label {
              font-size: 16px;
              margin-bottom: 10px;
              opacity: 0.9;
            }
            .instructions {
              background-color: #f3f4f6;
              padding: 20px;
              border-radius: 8px;
              margin: 20px 0;
              text-align: right;
            }
            .instructions h3 {
              color: #374151;
              margin-top: 0;
              font-size: 18px;
            }
            .instructions ol {
              color: #6b7280;
              line-height: 1.6;
            }
            .warning {
              background-color: #fef3c7;
              border: 1px solid #f59e0b;
              color: #92400e;
              padding: 15px;
              border-radius: 8px;
              margin: 20px 0;
              text-align: center;
            }
            .footer {
              background-color: #f9fafb;
              padding: 20px;
              text-align: center;
              color: #6b7280;
              font-size: 14px;
            }
            .app-name {
              color: #667eea;
              font-weight: bold;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>🎓 Legal 2025</h1>
              <p>تطبيق طلاب كلية الحقوق</p>
            </div>
            
            <div class="content">
              <div class="greeting">
                مرحباً ${displayName || 'عزيزي المستخدم'}،
              </div>
              
              <p>تم طلب كود التحقق لتفعيل حسابك في تطبيق <span class="app-name">Legal 2025</span></p>
              
              <div class="code-container">
                <div class="code-label">كود التحقق الخاص بك:</div>
                <div class="code">${code}</div>
              </div>
              
              <div class="instructions">
                <h3>خطوات التفعيل:</h3>
                <ol>
                  <li>ارجع إلى تطبيق Legal 2025</li>
                  <li>أدخل كود التحقق في الحقول المخصصة</li>
                  <li>اضغط على "تأكيد" لإكمال التسجيل</li>
                </ol>
              </div>
              
              <div class="warning">
                ⚠️ هذا الكود صالح لمدة 10 دقائق فقط
              </div>
              
              <p style="color: #6b7280; font-size: 14px;">
                إذا لم تطلب هذا الكود، يرجى تجاهل هذه الرسالة.
              </p>
            </div>
            
            <div class="footer">
              <p>هذه رسالة تلقائية من تطبيق <span class="app-name">Legal 2025</span></p>
              <p>© 2025 Legal 2025. جميع الحقوق محفوظة.</p>
            </div>
          </div>
        </body>
        </html>
      `,
    };

    // إرسال الإيميل
    await mailTransporter.sendMail(mailOptions);

    console.log(`تم إرسال كود التحقق بنجاح إلى: ${email}`);
    return { success: true, message: 'تم إرسال كود التحقق بنجاح' };

  } catch (error) {
    console.error('خطأ في إرسال كود التحقق:', error);
    throw new functions.https.HttpsError('internal', 'فشل في إرسال كود التحقق');
  }
});

// دالة إرسال إيميل ترحيب
exports.sendWelcomeEmail = functions.https.onCall(async (data, context) => {
  try {
    const { email, displayName } = data;

    if (!email || !displayName) {
      throw new functions.https.HttpsError('invalid-argument', 'البريد الإلكتروني والاسم مطلوبان');
    }

    const mailOptions = {
      from: `Legal 2025 <${gmailEmail}>`,
      to: email,
      subject: 'مرحباً بك في Legal 2025! 🎓',
      html: `
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
          <meta charset="UTF-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>مرحباً بك - Legal 2025</title>
          <style>
            body {
              font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
              background-color: #f8fafc;
              margin: 0;
              padding: 20px;
              direction: rtl;
            }
            .container {
              max-width: 600px;
              margin: 0 auto;
              background-color: white;
              border-radius: 12px;
              box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
              overflow: hidden;
            }
            .header {
              background: linear-gradient(135deg, #10b981 0%, #059669 100%);
              color: white;
              padding: 40px 30px;
              text-align: center;
            }
            .header h1 {
              margin: 0;
              font-size: 32px;
              font-weight: bold;
            }
            .content {
              padding: 40px 30px;
            }
            .welcome-message {
              font-size: 20px;
              color: #374151;
              text-align: center;
              margin-bottom: 30px;
            }
            .features {
              background-color: #f3f4f6;
              padding: 25px;
              border-radius: 12px;
              margin: 25px 0;
            }
            .features h3 {
              color: #374151;
              margin-top: 0;
              text-align: center;
              font-size: 20px;
            }
            .feature-list {
              list-style: none;
              padding: 0;
            }
            .feature-list li {
              padding: 10px 0;
              border-bottom: 1px solid #e5e7eb;
              color: #6b7280;
              font-size: 16px;
            }
            .feature-list li:last-child {
              border-bottom: none;
            }
            .feature-list li::before {
              content: "✅ ";
              margin-left: 10px;
            }
            .cta {
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              color: white;
              padding: 20px;
              border-radius: 12px;
              text-align: center;
              margin: 30px 0;
            }
            .footer {
              background-color: #f9fafb;
              padding: 20px;
              text-align: center;
              color: #6b7280;
              font-size: 14px;
            }
            .app-name {
              color: #10b981;
              font-weight: bold;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>🎉 مرحباً بك!</h1>
              <p>تم تفعيل حسابك بنجاح في Legal 2025</p>
            </div>
            
            <div class="content">
              <div class="welcome-message">
                أهلاً وسهلاً <strong>${displayName}</strong>!<br>
                نحن سعداء لانضمامك إلى مجتمع طلاب كلية الحقوق
              </div>
              
              <div class="features">
                <h3>🚀 ما يمكنك فعله الآن:</h3>
                <ul class="feature-list">
                  <li>تصفح المواد الدراسية والملفات</li>
                  <li>المشاركة في المجتمع الطلابي</li>
                  <li>الحصول على آخر الأخبار والإعلانات</li>
                  <li>التواصل مع زملائك الطلاب</li>
                  <li>الوصول إلى الموارد التعليمية</li>
                </ul>
              </div>
              
              <div class="cta">
                <h3>🎓 ابدأ رحلتك التعليمية الآن!</h3>
                <p>افتح التطبيق واستكشف جميع الميزات المتاحة</p>
              </div>
              
              <p style="text-align: center; color: #6b7280;">
                إذا كان لديك أي أسئلة، لا تتردد في التواصل معنا
              </p>
            </div>
            
            <div class="footer">
              <p>شكراً لاختيارك <span class="app-name">Legal 2025</span></p>
              <p>© 2025 Legal 2025. جميع الحقوق محفوظة.</p>
            </div>
          </div>
        </body>
        </html>
      `,
    };

    await mailTransporter.sendMail(mailOptions);
    console.log(`تم إرسال إيميل الترحيب بنجاح إلى: ${email}`);
    return { success: true, message: 'تم إرسال إيميل الترحيب بنجاح' };

  } catch (error) {
    console.error('خطأ في إرسال إيميل الترحيب:', error);
    throw new functions.https.HttpsError('internal', 'فشل في إرسال إيميل الترحيب');
  }
});
